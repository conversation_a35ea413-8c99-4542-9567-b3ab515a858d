{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Chrome",
            "request": "launch",
            "type": "chrome",
            "url": "http://localhost:3000",
            "webRoot": "${workspaceFolder}"
        },

        {
            "name": "Edge",
            "request": "launch",
            "type": "msedge",
            "url": "http://localhost:3000",
            "webRoot": "${workspaceFolder}"
        },
        {
            "name": "Current File (node)",
            "type": "node",
            "request": "launch",
            "program": "${file}",
            "cwd": "${fileDirname}",
            "autoAttachChildProcesses": true,
            "smartStep": true,
            "stopOnEntry": false,
            "skipFiles": ["<node_internals>/**/*"],
            "console": "integratedTerminal",
            "internalConsoleOptions": "neverOpen",
            "env": {
                "STAGE": "dev",
                "AWS_PROFILE": "pat-dev",
                "AWS_REGION": "us-west-2",
                "IS_LOCAL": "true"
            },
            "args": []
        },
        {
            "name": "Current File (TypeScript)",
            "type": "node",
            "request": "launch",
            "program": "${workspaceFolder}/node_modules/.bin/tsx",
            "args": ["${file}"],
            "cwd": "${fileDirname}",
            "autoAttachChildProcesses": true,
            "smartStep": true,
            "stopOnEntry": false,
            "skipFiles": ["<node_internals>/**/*"],
            "console": "integratedTerminal",
            "internalConsoleOptions": "neverOpen",
            "env": {
                "STAGE": "dev",
                "AWS_PROFILE": "pat-dev",
                "AWS_REGION": "us-west-2",
                "IS_LOCAL": "true"
            },
            "runtimeArgs": ["--experimental-vm-modules"],
            "sourceMaps": true
        },
        {
            "name": "Current File (jest)",
            "type": "node",
            "request": "launch",
            "program": "${workspaceFolder}/node_modules/.bin/jest",
            "cwd": "${fileDirname}",
            "args": ["--runInBand", "--testTimeout=60000", "--colors", "--verbose", "${file}"],
            "runtimeArgs": ["--experimental-vm-modules"],
            "env": {
                "STAGE": "dev",
                "AWS_REGION": "us-west-2",
                "AWS_PROFILE": "pat-dev"
            },
            "console": "integratedTerminal"
            //"internalConsoleOptions": "neverOpen",
            // "skipFiles": [
            //     "<node_internals>/**/*"
            // ],
        },
        {
            "name": "Current File (autogen-dev-team-py)",
            "type": "debugpy",
            "request": "launch",
            "justMyCode": false,
            "stopOnEntry": false,
            "program": "${file}",
            "cwd": "${workspaceFolder}/packages/autogen-dev-team-py/tests/samples/financial-import-py",
            "console": "integratedTerminal",
            "env": {
                "OPENAI_API_KEY": "***************************************************",
                "ANTHROPIC_API_KEY": "************************************************************************************************************",
                "LOCAL_MONGODB_URI": "mongodb://localhost:27017/pat-location",
                "MONGODB_URI": "mongodb+srv://pat_app:<EMAIL>/dev-pat-financial?retryWrites=true&w=majority",
                "AWS_PROFILE": "pat-dev",
                "AWS_REGION": "us-west-2",
                "IS_LOCAL": "true"
            },
            "args": ["mypy"]
        },
        {
            "name": "Current File (financial-tools-py)",
            "type": "debugpy",
            "request": "launch",
            "justMyCode": false,
            "stopOnEntry": false,
            "program": "${file}",
            "cwd": "${fileDirname}",
            "console": "integratedTerminal",
            "env": {
                "OPENAI_API_KEY": "***************************************************",
                "ANTHROPIC_API_KEY": "************************************************************************************************************",
                "LOCAL_MONGODB_URI": "mongodb://localhost:27017/pat-location",
                "MONGODB_URI": "mongodb+srv://pat_app:<EMAIL>/dev-pat-financial?retryWrites=true&w=majority",
                "AWS_PROFILE": "pat-dev",
                "AWS_REGION": "us-west-2",
                "IS_LOCAL": "true"
            }
        },
        {
            "name": "PatNextWeb: debug server-side",
            "type": "node-terminal",
            "request": "launch",
            "cwd": "${workspaceFolder}/apps/pat-web-next",
            "command": "npm run dev"
        },
        {
            "name": "PatNextWeb: debug client-side",
            "type": "chrome",
            "request": "launch",
            "url": "http://localhost:3000"
        },
        {
            "name": "PatNextWeb: debug full stack",
            "type": "node",
            "request": "launch",
            "program": "${workspaceFolder}/apps/pat-web-next/node_modules/next/dist/bin/next",
            "cwd": "${workspaceFolder}/apps/pat-web-next",
            "runtimeArgs": ["--inspect"],
            "skipFiles": ["<node_internals>/**"],
            "serverReadyAction": {
                "action": "debugWithEdge",
                "killOnServerStop": true,
                "pattern": "- Local:.+(https?://.+)",
                "uriFormat": "%s",
                "webRoot": "${workspaceFolder}/apps/finance-web"
            }
        }
    ]
}
