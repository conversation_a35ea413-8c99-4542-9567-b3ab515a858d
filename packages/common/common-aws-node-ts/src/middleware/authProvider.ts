import type { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { IGlobalContext } from "../interfaces/context";
import { MiddlewareFn, MiddlewareObj } from "./middleware-host";
import { IdTokenPayload } from "../types/tokens";
import { ObjectId } from "mongodb";

export function authProvider({requiresAuth = true}): MiddlewareObj<APIGatewayProxyEvent, APIGatewayProxyResult> {
    const before: MiddlewareFn<APIGatewayProxyEvent, APIGatewayProxyResult> = async (request): Promise<undefined> => {
        if (!(request.context as any).globalContext ) {
            throw new Error("globalContext not found");
        }
        else {
            if (requiresAuth) {
                const globalContext = (request.context as any).globalContext as IGlobalContext;

                if (!request.event.requestContext?.authorizer) {
                    throw new Error("AuthorizationError");
                }   

                const claims = (
                    request.event.requestContext?.authorizer?.["claims"] 
                    // || request.event.requestContext?.authorizer?.["jwt"]?.claims
                ) as IdTokenPayload;
                console.log(JSON.stringify(claims, null, 4));

                globalContext.updateSession({
                    user_id: new ObjectId(claims?.user_id),
                    session_id: claims?.jti,
                    permissions: ["*"],
                    metadata: {
                        // todo: deviceId
                    },
                });
                console.log(JSON.stringify(globalContext.session, null, 4));
            }               
        }
    };

    return {    
        before,
    };
}
