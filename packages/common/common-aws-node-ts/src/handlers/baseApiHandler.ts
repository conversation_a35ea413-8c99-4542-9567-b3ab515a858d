import type { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { authProvider } from "../middleware/authProvider";
import { eventBodyDeserializer } from "../middleware/event-body-deserializer";
import { globalContextProvider } from "../middleware/globalContextProvider";
import { httpRouteHandler, Route } from "../middleware/http-router";
import { middlewareHost } from "../middleware/middleware-host";

export const createBaseApiHandler = (
    configSchema: any, 
    apiRoutes: Route<APIGatewayProxyEvent, APIGatewayProxyResult>[],
    requiresAuth = false
) => {
    if (requiresAuth) {
        const handler = middlewareHost<APIGatewayProxyEvent, APIGatewayProxyResult>()
            .use(globalContextProvider(configSchema))
            .use(eventBodyDeserializer())
            .use(authProvider({ requiresAuth }))        
            .handler(httpRouteHandler(apiRoutes));
        return handler;
    }

    const handler = middlewareHost<APIGatewayProxyEvent, APIGatewayProxyResult>()
        .use(globalContextProvider(configSchema))
        .use(eventBodyDeserializer())
        .handler(httpRouteHandler(apiRoutes));
    return handler;
};
