export type IdTokenPayload = {
    sub: string;
    email_verified: boolean;
    iss: string;
    phone_number_verified?: boolean;
    "cognito:username": string;
    origin_jti: string;
    aud: string;
    event_id: string;
    user_id: string;
    token_use: string;
    auth_time: number;
    phone_number?: string;
    exp: number;
    iat: number;
    jti: string;
    email: string;
};

export class IdToken {
    jwtToken: string;
    payload: IdTokenPayload;

    constructor(data: { jwtToken: string; payload: IdTokenPayload }) {
        this.jwtToken = data.jwtToken;
        this.payload = data.payload;
    }
}

export class RefreshToken {
    token: string;

    constructor(data: { token: string }) {
        this.token = data.token;
    }
}

export class Auth {
    isAuthenticated: boolean;
    username: string;
    user: string;
    idToken: IdToken;
    refreshToken: RefreshToken;
    deviceId: string;
    error: string | null;

    constructor(data: {
        isAuthenticated: boolean;
        username: string;
        user: string;
        idToken: IdToken;
        refreshToken: RefreshToken;
        deviceId: string;
        error: string | null;
    }) {
        this.isAuthenticated = data.isAuthenticated;
        this.username = data.username;
        this.user = data.user;
        this.idToken = new IdToken({
            jwtToken: data.idToken.jwtToken,
            payload: data.idToken.payload,
        });
        this.refreshToken = new RefreshToken({
            token: data.refreshToken.token,
        });
        this.deviceId = data.deviceId;
        this.error = data.error;
    }
}
