// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
// SPDX-License-Identifier: MIT-0
import { useState } from "react";
import { useDisclaimerFlashbarItem } from "./disclaimer-flashbar-item";
import { useId } from "./use-id";

export function useNotifications(successNotification) {
    const successId = useId();
    const [successDismissed, dismissSuccess] = useState(false);
    const [disclaimerDismissed, dismissDisclaimer] = useState(false);

    const disclaimerItem = useDisclaimerFlashbarItem(() => dismissDisclaimer(true));

    const notifications = [];

    if (disclaimerItem && !disclaimerDismissed) {
        notifications.push(disclaimerItem);
    }

    if (successNotification & !successDismissed) {
        notifications.push({
            type: "success",
            content: "Resource created successfully",
            statusIconAriaLabel: "success",
            dismissLabel: "Dismiss message",
            dismissible: true,
            onDismiss: () => dismissSuccess(true),
            id: successId,
        });
    }

    return notifications;
}
