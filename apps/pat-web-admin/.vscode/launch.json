{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "chrome",
            "request": "launch",
            "type": "chrome",
            "url": "http://localhost:3000",
            "webRoot": "${workspaceFolder}"
        },

        {
            "name": "edge",
            "request": "launch",
            "type": "msedge",
            "url": "http://localhost:3000",
            "webRoot": "${workspaceFolder}"
        },
        {
            "name": "current (py)",
            "type": "debugpy",
            "request": "launch",
            "justMyCode": false,
            "program": "${file}",
            "cwd": "${fileDirname}",
            "console": "integratedTerminal",
            "env": {
                "OPENAI_API_KEY": "***************************************************",
                "MONGO_URI": "mongodb://localhost:27017/pat-location",
                "AWS_PROFILE": "pat-dev",
                "AWS_REGION": "us-west-2",
                "IS_LOCAL": "true"
            }
        },
        {
            "name": "current (node)",
            "type": "node",
            "request": "launch",
            "program": "${file}",
            "cwd": "${fileDirname}",
            "autoAttachChildProcesses": true,
            "smartStep": true,
            "stopOnEntry": false,
            "skipFiles": ["<node_internals>/**/*"],
            "console": "integratedTerminal",
            "internalConsoleOptions": "neverOpen",
            "env": {
                "STAGE": "dev",
                "AWS_PROFILE": "pat-dev",
                "AWS_REGION": "us-west-2",
                "IS_LOCAL": "true"
            },
            "args": []
        },
        {
            "name": "current (jest)",
            "type": "node",
            "request": "launch",
            "program": "${workspaceFolder}/node_modules/.bin/jest",
            "cwd": "${fileDirname}",
            "args": ["--runInBand", "--testTimeout=60000", "--colors", "--verbose", "${file}"],
            "runtimeArgs": ["--experimental-vm-modules"],
            "env": {
                "STAGE": "dev",
                "AWS_REGION": "us-west-2",
                "AWS_PROFILE": "pat-dev"
            },
            "console": "integratedTerminal"
            //"internalConsoleOptions": "neverOpen",
            // "skipFiles": [
            //     "<node_internals>/**/*"
            // ],
        }
    ]
}
