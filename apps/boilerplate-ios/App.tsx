import merge from "deepmerge";
import React from "react";
import { FileLogger } from "react-native-file-logger";

FileLogger.configure();

import LoginView from "@psprowls/core-app-client-native/LoginView.js";
import { useLocalNotifications } from "@psprowls/core-app-client-native/localNotifications.js";
import { getNativeUtils } from "@psprowls/core-app-client-native/nativeUtils.js";
import { usePushNotifications } from "@psprowls/core-app-client-native/pushNotifications.js";
import SystemView from "@psprowls/core-app-client-native/SystemView";
import { AuthContext, useAuth } from "@psprowls/core-app-client-shared/auth.js";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import {
    NavigationContainer,
    DarkTheme as NavigationDarkTheme,
    useNavigationContainerRef,
} from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { MD3DarkTheme, MD3LightTheme, Provider as PaperProvider } from "react-native-paper";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";

// import LocationsView from '@psprowls/location-client-native/LocationsView.js';
// import LocationsMapView from '@psprowls/location-client-native/LocationsMapView';
// import {LocationsDataProvider} from '@psprowls/location-client-shared/locationsQuery.js';

// import HealthSamplesView from '@psprowls/health-kit-client-native/healthSamplesView';
// import {HealthDataProvider} from '@psprowls/health-kit-client-shared/healthSamplesQuery.js';

// import {
//     useSharingExtension,
//     SharingExtensionContext,
// } from '@psprowls/sharing-ext-native-ios/sharingExtension.js';

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const Tab = createBottomTabNavigator();
// const LocationsStack = createStackNavigator();

// Get Native utilities for deviceInfo, localStorage, etc...
const _native = getNativeUtils();

const queryClient = new QueryClient();

function App() {
    console.log("App:render");

    // Auth...
    const auth = useAuth(_native);
    // Local Notifications (aka Reminders)...
    const _localNotifications = useLocalNotifications();
    // Push Notifications
    const _pushNotifications = usePushNotifications();
    // Share Extension Handler
    // const sharingExtension = useSharingExtension(
    //     {
    //         sharingMedia: 'Timeline',
    //         onShare,
    //     },
    //     auth,
    // );
    // const navigationRef = useNavigationContainerRef();
    // const onShare = () => {
    //     console.log('onShare');
    //     navigationRef.navigate('System');
    // };

    // Theme...
    const CombinedDefaultTheme = merge(MD3DarkTheme, NavigationDarkTheme);
    let theme = {
        ...CombinedDefaultTheme,
        colors: {
            ...CombinedDefaultTheme.colors,
            category6: "#ffb2b9",
            onCategory6: "#67001f",
            category6Container: "#91002f",
            onCategory6Container: "#ffdadc",
            category5: "#ffb780",
            onCategory5: "#4e2600",
            category5Container: "#6f3800",
            onCategory5Container: "#ffdcc4",
            category4: "#e4c44a",
            onCategory4: "#3b2f00",
            category4Container: "#554500",
            onCategory4Container: "#ffe176",
            category3: "#00e385",
            onCategory3: "#00391d",
            category3Container: "#00522c",
            onCategory3Container: "#5affa2",
            category2: "#c7bfff",
            onCategory2: "#29009f",
            category2Container: "#3d00dd",
            onCategory2Container: "#e4dfff",
            category1: "#d5bbff",
            onCategory1: "#41008b",
            category1Container: "#5d00c2",
            onCategory1Container: "#ebdcff",
            // category1: '#be0040',
            // onCategory1: '#ffffff',
            // category1Container: '#ffdadc',
            // onCategory1Container: '#400010',
            // category2: '#924c00',
            // onCategory2: '#ffffff',
            // category2Container: '#ffdcc4',
            // onCategory2Container: '#2f1400',
            // category3: '#715d00',
            // onCategory3: '#ffffff',
            // category3Container: '#ffe176',
            // onCategory3Container: '#221b00',
            // category4: '#006d3d',
            // onCategory4: '#ffffff',
            // category4Container: '#5affa2',
            // onCategory4Container: '#00210f',
            // category5: '#5631fd',
            // onCategory5: '#ffffff',
            // category5Container: '#e4dfff',
            // onCategory5Container: '#170065',
            // category6: '#7b0df8',
            // onCategory6: '#ffffff',
            // category6Container: '#ebdcff',
            // onCategory6Container: '#270058',
        },
        // colors: {
        //     category1: '#be0040',
        //     onCategory1: '#ffffff',
        //     category1Container: '#ffdadc',
        //     onCategory1Container: '#400010',
        //     category2: '#924c00',
        //     onCategory2: '#ffffff',
        //     category2Container: '#ffdcc4',
        //     onCategory2Container: '#2f1400',
        //     category3: '#715d00',
        //     onCategory3: '#ffffff',
        //     category3Container: '#ffe176',
        //     onCategory3Container: '#221b00',
        //     category4: '#006d3d',
        //     onCategory4: '#ffffff',
        //     category4Container: '#5affa2',
        //     onCategory4Container: '#00210f',
        //     category5: '#5631fd',
        //     onCategory5: '#ffffff',
        //     category5Container: '#e4dfff',
        //     onCategory5Container: '#170065',
        //     category6: '#7b0df8',
        //     onCategory6: '#ffffff',
        //     category6Container: '#ebdcff',
        //     onCategory6Container: '#270058',
        // },
    };

    // Render App
    return (
        <AuthContext.Provider value={auth}>
            <QueryClientProvider client={queryClient}>
                {/* <HealthDataProvider>
                    <LocationsDataProvider>
                        <LocationsContext.Provider value={locations}> */}
                <PaperProvider theme={theme}>
                    <AppNavigationContainer
                        theme={theme}
                        // navigationRef={navigationRef}
                        auth={auth}
                    />
                </PaperProvider>
                {/* </LocationsContext.Provider>
                    </LocationsDataProvider>
                </HealthDataProvider> */}
            </QueryClientProvider>
        </AuthContext.Provider>
    );
}

// const LocationsStackScreen = () => {
//     return (
//         <LocationsStack.Navigator
//             initialRouteName="LocationsView"
//             screenOptions={{
//                 headerTitleStyle: {
//                     fontWeight: 'bold',
//                 },
//             }}>
//             <LocationsStack.Screen
//                 name="LocationsView"
//                 component={LocationsView}
//                 options={{
//                     title: 'Locations',
//                 }}
//             />
//             <LocationsStack.Screen
//                 name="LocationsMapView"
//                 component={LocationsMapView}
//                 options={({location}) => ({title: 'Location Map'})}
//             />
//         </LocationsStack.Navigator>
//     );
// };

const AppNavigationContainer = ({ theme, auth, navigationRef }) => {
    return (
        <NavigationContainer theme={theme} ref={navigationRef}>
            {auth.state.auth.isAuthenticated ? (
                <Tab.Navigator>
                    {/*<Tab.Screen
                        name="Locations"
                        component={LocationsStackScreen}
                        options={{
                            headerShown: false,
                            tabBarLabel: 'Locations',
                            tabBarIcon: ({color}) => (
                                <MaterialCommunityIcons
                                    name="map"
                                    color={color}
                                    size={26}
                                />
                            ),
                        }}
                    />
                    <Tab.Screen
                        name="Health"
                        component={HealthSamplesView}
                        options={{
                            tabBarLabel: 'Health',
                            tabBarIcon: ({color}) => (
                                <MaterialCommunityIcons
                                    name="weight-lifter"
                                    color={color}
                                    size={26}
                                />
                            ),
                        }}
                    /> */}
                    <Tab.Screen
                        name="System"
                        options={{
                            tabBarLabel: "System",
                            tabBarIcon: ({ color }) => (
                                <MaterialCommunityIcons name="cog-outline" color={color} size={26} />
                            ),
                        }}
                    >
                        {() => {
                            return <SystemView />;
                        }}
                    </Tab.Screen>
                </Tab.Navigator>
            ) : (
                <LoginView />
            )}
        </NavigationContainer>
    );
};

export const combineComponents = (...components) => {
    return components.reduce((AccumulatedComponents, CurrentComponent) => {
        return (
            ({ children }) => {
                return (
                    <AccumulatedComponents>
                        <CurrentComponent>{children}</CurrentComponent>
                    </AccumulatedComponents>
                );
            },
            ({ children }) => <>{children}</>
        );
    });
};

export default App;
