const { getDefaultConfig, mergeConfig } = require("@react-native/metro-config");
/**
 * Metro configuration for React Native
 * https://github.com/facebook/react-native
 *
 * @format
 */
const exclusionList = require("metro-config/src/defaults/exclusionList");
const { getMetroTools } = require("react-native-monorepo-tools");
const metroTools = getMetroTools();

console.log(metroTools.watchFolders);
console.log(metroTools.blockList);
console.log(metroTools.extraNodeModules);

const config = {
    transformer: {
        getTransformOptions: async () => ({
            transform: {
                experimentalImportSupport: false,
                // this defeats the RCTDeviceEventEmitter is not a registered callable module
                inlineRequires: true,
            },
        }),
    },
    // Add additional Yarn workspace package roots to the module map.
    // This allows importing importing from all the project's packages.
    watchFolders: metroTools.watchFolders,
    resolver: {
        // Ensure we resolve nohoist libraries from this directory.
        blockList: exclusionList(metroTools.blockList),
        extraNodeModules: metroTools.extraNodeModules,
    },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
