"use strict";



const handleGenericGetRequest = async ({ collection }, { db, logger }, { auth, params }, query = {}) => {
    let results = { items: [] };

    let { id, last_id, last_date, start_date, end_date, timestamp } = params;
    let { limit = 25, sort_field = "startDate", sort = 1, skip = 0, page = 0 } = params;
    let { statistics = false } = params;

    if (params.limit) {
        limit = parseInt(limit);
    }
    if (params.sort) {
        sort = parseInt(params.sort);
    }
    if (params.skip) {
        skip = parseInt(params.skip);
    }
    if (params.page) {
        page = parseInt(params.page);
    }

    statistics = params.statistics ? true : false;

    query.user = auth.user_id || auth.user // (old style...support just in case...)
    // the following params are mutually exclusive
    if (id) {
        query._id = id;
    } else if (last_id) {
        if (sort === 1) {
            query._id = { $gt: last_id };
        } else {
            query._id = { $lt: last_id };
        }
    }
    if (timestamp) {
        query.timestamp = { $gte: timestamp };
    } else if (start_date && end_date) {
        query.$and = [
            { startDate: { $gt: new Date(start_date.getTime() - 1000 * 24 * 60 * 60), $lt: end_date } },
            { $or: [{ endDate: null }, { endDate: { $gt: start_date } }] },
        ];
        if (last_date) {
            query.$and.push({ endDate: sort === 1 ? { $gt: last_date } : { $lt: last_date } });
        }
    } else if (last_date) {
        query.startDate = sort === 1 ? { $gt: last_date } : { $lt: last_date };
    }
    if (page >= 0) {
        skip = page * limit;
    }

    // const projection = {
    //     _id: 1,
    //     user: 1,
    //     startDate: 1,
    //     endDate: 1,
    //     type: 1,
    //     value: 1,
    //     details: 1,
    //     sourceName: 1,
    //     sourceId: 1,
    //     metadata: 1,
    // };

    logger.info(`MONGO-QUERY: ${JSON.stringify(query)}`);

    if (statistics) {
        const count = await db.collection(collection).countDocuments(query);
        results.statistics = { total: count };
    }

    if (id) {
        results.items = await db
            .collection(collection)
            .find(query)
            // .project(projection)
            .toArray();
    } else {
        // endDate > startDate && startDate < endDate
        results.items = await db
            .collection(collection)
            .find(query)
            // .project(projection)
            .limit(limit)
            .skip(skip)
            .sort({ [sort_field]: sort })
            .toArray();
    }
    logger.info(`MONGO-RESULTS: ${results.items.length}`);
    console.log(JSON.stringify(results.items));
    if (results.items.length === 0) {
        results.ok = true;
    }
    return results;
};

const handleGenericTimestampGetRequest = async (
    { collection },
    { db, logger },
    { auth, params },
    query = {},
) => {
    let results = { items: [] };

    let {
        id,
        last_id,
        last_date,
        start_date,
        end_date,
        limit = null,
        sort_field = "timestamp",
        sort = 1,
        statistics = false,
        skip = 0,
        page = -1,
    } = params;

    if (params.limit) {
        limit = parseInt(limit);
    }
    if (params.sort) {
        sort = parseInt(params.sort);
    }
    if (params.skip) {
        skip = parseInt(params.skip);
    }
    if (params.page) {
        page = parseInt(params.page);
    }

    statistics = params.statistics ? true : false;

    if (page >= 0) {
        skip = page * limit;
    }

    // the following params are mutually exclusive
    query.user = auth.user;
    // the following params are mutually exclusive
    if (id) {
        query._id = id;
    } else if (last_id) {
        if (sort === 1) {
            query._id = { $gt: last_id };
        } else {
            query._id = { $lt: last_id };
        }
    }
    if (start_date && end_date) {
        query.$and = [{ timestamp: { $gt: start_date } }, { timestamp: { $lt: end_date } }];
        if (last_date) {
            query.$and.push({ timestamp: sort === 1 ? { $gt: last_date } : { $lt: last_date } });
        }
    } else if (last_date) {
        query.timestamp = sort === 1 ? { $gt: last_date } : { $lt: last_date };
    }
    if (page >= 0) {
        skip = page * limit;
    }

    if (statistics) {
        const count = await db.collection(collection).countDocuments(query);
        results.statistics = { total: count };
    }
    logger.info(`MONGO-QUERY: ${JSON.stringify(query)}`);
    if (id) {
        results.items = await db.collection(collection).find(query).toArray();
    } else {
        // endDate > startDate && startDate < endDate
        if (limit) {
            results.items = await db
                .collection(collection)
                .find(query)
                .sort({ [sort_field]: sort })
                .skip(skip)
                .limit(limit)
                .toArray();
        } else {
            results.items = await db
                .collection(collection)
                .find(query)
                .sort({ [sort_field]: sort })
                .skip(skip)
                .toArray();
        }
    }
    logger.info(`MONGO-RESULTS: ${results.items.length}`);

    if (results.items.length === 0) {
        results.ok = true;
    }
    return results;
};

const handleGenericDeleteRequest = async ({ collection }, { db, logger }, { auth, params }) => {
    const result = { deleted: [], errors: [] };
    const { id } = params;
    const query = { user: auth.user, _id: id };
    logger.info(`MONGO-QUERY: ${JSON.stringify(query)}`);
    const existing = await db.collection(collection).findOne(query);
    logger.info(`MONGO-RESULTS: ${existing ? "1" : "0"}`);
    if (existing) {
        logger.info(`MONGO-DELETE: ${JSON.stringify(query)}`);
        const response = await db.collection(collection).deleteOne(query);
        logger.info(`MONGO-RESPONSE: ${JSON.stringify(response)}`);
        result.deleted = [existing._id];
    } else {
        return { errors: ["not_found"] };
    }
    return result;
};

const handleGenericPutRequest = async ({ collection }, { db, logger }, { auth, data, metadata }) => {
    logger.info(`handleGenericPutRequest: ${JSON.stringify(data)}`);
    const result = { updated: [], errors: [] };
    const query = { _id: data._id };
    logger.info(`MONGO-QUERY: ${JSON.stringify(query)}`);
    const existing = await db.collection(collection).findOne(query);
    logger.info(`MONGO-RESULT: ${existing ? "1" : "0"}`);
    logger.info(`existing: ${JSON.stringify(existing)}`);
    if (existing) {
        data.modified = new Date();
        logger.info(`MONGO-UPDATE: ${JSON.stringify(query)}`);
        const response = await db.collection(collection).updateOne(query, { $set: data });
        logger.info(`MONGO-RESPONSE: ${JSON.stringify(response)}`);
        result.updated = [existing._id];
    } else {
        return { errors: ["not_found"] };
    }
    return result;
};

const handleGenericPostRequest = async ({ collection }, { db, logger }, { auth, data }) => {
    const result = { added: [], errors: [] };
    data.user = auth.user;
    data.created = new Date();
    let query = {};
    if (data.uuid || data.id) {
        if (data.uuid) {
            query = { uuid: data.uuid };
        } else {
            query = { id: data.id };
        }
        const existing = await db.collection(collection).findOne(query);
        if (existing) {
            logger.warn(`skipping duplicate uuid found for: ${data.uuid || data.id}`);
            result.ok = true;
            return result;
        }
    }
    logger.info(`MONGO-INSERT: ${JSON.stringify(data)}`);
    const response = await db.collection(collection).insertOne(data);
    logger.info(`MONGO-RESPONSE: ${JSON.stringify(response)}`);
    result.added.push(response.insertedId);
    return result;
};

export {
    handleGenericGetRequest,
    handleGenericDeleteRequest,
    handleGenericPutRequest,
    handleGenericPostRequest,
    handleGenericTimestampGetRequest,
};
